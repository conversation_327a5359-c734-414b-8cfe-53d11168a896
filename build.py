"""
打包脚本 - 用于生成可执行文件
"""
import os
import sys
import subprocess
import shutil
from datetime import datetime

def build_exe():
    """使用PyInstaller打包程序为可执行文件"""
    print("开始打包程序...")
    
    # 创建必要的目录
    for directory in ["build", "dist", "assets", "plugins"]:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"已创建目录: {directory}")
    
    # 如果 assets 目录为空，添加一个空文件确保目录存在
    if not os.listdir("assets"):
        with open("assets/.placeholder", "w") as f:
            f.write("# 占位文件，确保目录不为空")
        print("已创建占位文件到 assets 目录")
        
    # 版本信息
    version = datetime.now().strftime("%Y%m%d%H%M")
    
    # 图标文件
    icon_file = ""
    if os.path.exists("assets/icon.ico"):
        icon_file = "--icon=assets/icon.ico"
    
    # 根据操作系统设置不同的分隔符
    separator = ";" if sys.platform.startswith("win") else ":"
    add_data_param = f"--add-data=assets{separator}assets"
    
    # 打包命令
    cmd = [
        "pyinstaller",
        "--noconfirm",
        "--onefile",
        "--windowed",
    ]
    
    # 添加图标参数（如果存在）
    if icon_file:
        cmd.append(icon_file)
    
    # 添加其他参数
    cmd.extend([
        add_data_param,
        f"--name=财务数据处理工具_v{version}",
        "--distpath=./dist",
        "--workpath=./build",
        "main.py"
    ])
    
    try:
        # 打印将要执行的命令
        print(f"执行命令: {' '.join(cmd)}")
        
        # 运行PyInstaller
        subprocess.run(cmd, check=True)
        
        print(f"打包完成，可执行文件已生成在 dist 目录中。")
        
        # 复制示例文件到dist目录
        if os.path.exists("example_files"):
            if hasattr(shutil, 'copytree') and callable(getattr(shutil.copytree, '__defaults__', None)):
                # Python 3.8+
                shutil.copytree("example_files", f"dist/示例文件", dirs_exist_ok=True)
            else:
                # Python 3.7 及更早版本
                if os.path.exists(f"dist/示例文件"):
                    shutil.rmtree(f"dist/示例文件")
                shutil.copytree("example_files", f"dist/示例文件")
            print("已复制示例文件到dist目录中。")
            
        # 创建插件目录
        os.makedirs("dist/plugins", exist_ok=True)
        
        # 复制README文件
        if os.path.exists("README.md"):
            shutil.copy("README.md", "dist/README.txt")
            print("已复制README文件到dist目录中。")
            
        # 显示生成的文件
        print("\n生成的文件:")
        for file in os.listdir("dist"):
            print(f" - {file}")
            
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    except Exception as e:
        print(f"发生错误: {e}")
        return False
        
    return True

if __name__ == "__main__":
    build_exe() 