name: 构建 Windows 可执行文件 (中国网络优化)

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  # 允许手动触发工作流
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: 安装依赖 (使用国内镜像)
      run: |
        python -m pip install --upgrade pip
        pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
        pip install -r requirements.txt
        pip install pyinstaller
    
    - name: 创建必要的目录
      run: |
        if (!(Test-Path "assets")) { New-Item -ItemType Directory -Force -Path "assets" }
        if (!(Test-Path "plugins")) { New-Item -ItemType Directory -Force -Path "plugins" }
        if (!(Test-Path "example_files")) { New-Item -ItemType Directory -Force -Path "example_files" }
        if ((Get-ChildItem -Path "assets" -Force | Measure-Object).Count -eq 0) {
          Set-Content -Path "assets\.placeholder" -Value "# 占位文件，确保目录不为空"
        }
    
    - name: 构建可执行文件
      run: |
        $version = Get-Date -Format "yyyyMMddHHmm"
        $exeName = "FinanceDataProcessor_v$version"
        
        pyinstaller --noconfirm `
          --onefile `
          --windowed `
          --add-data "assets;assets" `
          --name "$exeName" `
          --distpath "./dist" `
          --workpath "./build" `
          main.py
        
        # 复制示例文件和README到dist目录
        if (Test-Path "example_files") {
          Copy-Item -Path "example_files" -Destination "dist\ExampleFiles" -Recurse -Force
        }
        
        # 创建插件目录
        New-Item -ItemType Directory -Force -Path "dist\plugins"
        
        # 复制README文件
        if (Test-Path "README.md") {
          Copy-Item -Path "README.md" -Destination "dist\README.txt" -Force
        }
        
        # 重命名中文文件
        Rename-Item -Path "dist\$exeName.exe" -NewName "财务数据处理工具.exe" -Force
        
        Write-Host "构建完成! 可执行文件已生成在 dist 目录中"
    
    - name: 打包发布文件
      run: |
        Compress-Archive -Path "dist\*" -DestinationPath "财务数据处理工具.zip" -Force
    
    - name: 上传构建结果
      uses: actions/upload-artifact@v4
      with:
        name: 财务数据处理工具-Windows版本
        path: 财务数据处理工具.zip 