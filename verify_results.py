#!/usr/bin/env python3
"""
验证处理结果的脚本
"""
import pandas as pd

def main():
    """主函数"""
    print("验证Excel处理结果...")
    
    # 读取输出文件
    try:
        df = pd.read_excel('test_output.xlsx')
        print(f"\n成功读取输出文件，共 {len(df)} 行数据")
        
        # 显示所有列名
        print(f"\n列名: {list(df.columns)}")
        
        # 显示详细结果
        print("\n详细处理结果:")
        print("="*120)
        
        for index, row in df.iterrows():
            print(f"\n行 {index + 1}:")
            print(f"  科目名称: {row['科目名称']}")
            print(f"  部门段: {row['部门段']}")
            print(f"  金额（万元）: {row['金额（万元）']}")
            print(f"  预算明细项: {row['预算明细项']}")
            print(f"  运营费用大类: {row['运营费用大类']}")
            print(f"  部门: {row['部门']}")
            print(f"  部门类别: {row['部门类别']}")
            print(f"  是否预算: {row['是否预算']}")
            print(f"  是否可控: {row['是否可控']}")
            print(f"  是否一盘货: {row['是否一盘货']}")
            
        print("\n" + "="*120)
        
        # 验证特定逻辑
        print("\n验证处理逻辑:")
        
        # 验证财务费用
        finance_rows = df[df['科目名称'] == '财务费用']
        if not finance_rows.empty:
            row = finance_rows.iloc[0]
            print(f"✓ 财务费用: AH={row['预算明细项']}, AI={row['运营费用大类']}, AL={row['是否预算']}, AM={row['是否可控']}")
        
        # 验证办公用品
        office_rows = df[df['科目名称'] == '管理费用-办公费-办公用品']
        if not office_rows.empty:
            row = office_rows.iloc[0]
            print(f"✓ 办公用品: AH={row['预算明细项']}, AI={row['运营费用大类']}")
        
        # 验证差旅费
        travel_rows = df[df['科目名称'] == '管理费用-差旅费']
        if not travel_rows.empty:
            row = travel_rows.iloc[0]
            print(f"✓ 差旅费: AH={row['预算明细项']}, AI={row['运营费用大类']}")
        
        # 验证五险一金
        insurance_rows = df[df['科目名称'] == '管理费用-社会保险金-基本养老金']
        if not insurance_rows.empty:
            row = insurance_rows.iloc[0]
            print(f"✓ 五险一金: AH={row['预算明细项']}, AI={row['运营费用大类']}")
        
        # 验证部门映射
        print(f"\n部门映射验证:")
        for index, row in df.iterrows():
            print(f"  {row['部门段']} -> {row['部门']} -> {row['部门类别']}")
        
        # 验证一盘货逻辑
        print(f"\n一盘货验证:")
        for index, row in df.iterrows():
            has_yipanhuo = '一盘货' in str(row['行说明'])
            expected = 'Y' if has_yipanhuo else 'N'
            actual = row['是否一盘货']
            status = "✓" if expected == actual else "✗"
            print(f"  {status} 行说明: '{row['行说明']}' -> 是否一盘货: {actual} (期望: {expected})")
            
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
