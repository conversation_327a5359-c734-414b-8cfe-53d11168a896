"""
Excel数据处理器核心模块，包含数据处理逻辑
"""
import pandas as pd
from src.utils.config import COL_MAPPING, ALLOWED_L_SUBSTRINGS
from src.utils.helpers import get_output_filename
from src.utils.department_mapping import AJ_MAPPING, AK_MAPPING

class ExcelProcessor:
    """
    Excel数据处理器类，负责处理Excel文件中的财务数据
    """

    def __init__(self, logger):
        """
        初始化Excel处理器

        参数:
            logger: 日志对象，用于记录处理过程
        """
        self.logger = logger
        self.df = None
        self.input_file = None
        self.output_file = None

    def process_file(self, input_file, custom_output_file=None):
        """
        处理Excel文件的主函数

        参数:
            input_file: 输入文件路径
            custom_output_file: 自定义输出文件路径（可选）

        返回:
            布尔值，表示处理是否成功
        """
        try:
            self.input_file = input_file

            # 设置输出文件路径
            if custom_output_file:
                self.output_file = custom_output_file
            else:
                self.output_file = get_output_filename(input_file)

            self.logger.info(f"开始处理文件: {self.input_file}")

            # 读取Excel文件
            self.df = pd.read_excel(self.input_file)
            self.logger.info(f"成功读取 {len(self.df)} 行数据。")

            # 检查必要的列是否存在
            self._validate_columns()

            # 创建新列用于后续处理
            self._create_new_columns()

            # 数据清洗和筛选
            self._clean_and_filter_data()

            if self.df.empty:
                self.logger.warning("筛选后数据为空，处理终止。")
                return False

            # 计算费用金额
            self._calculate_expense_amount()

            # 处理费用项目映射
            self._process_expense_categories()

            # 处理AI列（运营费用大类）
            self._process_ai_column()

            # 处理AJ列（部门）
            self._process_aj_column()

            # 处理AK列（部门类别）
            self._process_ak_column()

            # 处理AL列（是否预算）
            self._process_al_column()

            # 处理AM列（是否可控）
            self._process_am_column()

            # 处理AN列（是否一盘货）
            self._process_an_column()

            # 保存结果
            self.df.to_excel(self.output_file, index=False, engine='openpyxl')
            self.logger.info(f"处理完成！结果已保存到: {self.output_file}")

            return True

        except FileNotFoundError:
            self.logger.error(f"错误：输入文件 {input_file} 未找到。请检查文件路径和名称。")
            return False
        except KeyError as e:
            self.logger.error(f"错误：映射的列名不存在: {e}。请检查列名映射。")
            return False
        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {e}")
            return False

    def _validate_columns(self):
        """验证必要的列是否存在"""
        for col_key in ['L', 'N', 'W', 'X', 'Z']:
            if COL_MAPPING[col_key] not in self.df.columns:
                self.logger.warning(f"警告: 列 '{COL_MAPPING[col_key]}' 不存在。")

        self.logger.info("列验证完成")

    def _create_new_columns(self):
        """创建新列用于后续处理"""
        for col in ['AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN']:
            if COL_MAPPING[col] not in self.df.columns:
                self.df[COL_MAPPING[col]] = None

        self.logger.info("已创建所需的新列")

    def _clean_and_filter_data(self):
        """数据清洗和筛选"""
        # 记录原始行数
        original_rows = len(self.df)

        # 1.1 删除 N 列为空数据的行
        self.df.dropna(subset=[COL_MAPPING['N']], inplace=True)
        self.logger.info(f"根据 {COL_MAPPING['N']} 列删除空值后剩余 {len(self.df)} 行 (删除了 {original_rows - len(self.df)} 行)。")

        # 1.2 L列保留包含指定费用类型的行，其余删除
        original_rows = len(self.df)

        # 创建筛选条件：包含任一指定字段
        filter_condition = self.df[COL_MAPPING['L']].astype(str).str.contains(ALLOWED_L_SUBSTRINGS[0], na=False)
        for substring in ALLOWED_L_SUBSTRINGS[1:]:
            filter_condition = filter_condition | self.df[COL_MAPPING['L']].astype(str).str.contains(substring, na=False)

        self.df = self.df[filter_condition]
        self.logger.info(f"根据 {COL_MAPPING['L']} 列筛选后剩余 {len(self.df)} 行 (删除了 {original_rows - len(self.df)} 行)。")

    def _calculate_expense_amount(self):
        """计算 费用金额(万元) (AG列)"""
        # 确保 W 和 X 列是数值类型，非数值转为NaN，然后填充0
        self.df[COL_MAPPING['W']] = pd.to_numeric(self.df[COL_MAPPING['W']], errors='coerce')
        self.df[COL_MAPPING['X']] = pd.to_numeric(self.df[COL_MAPPING['X']], errors='coerce')
        # 如果转换后出现NaN (比如原单元格是文本)，填充0
        self.df[[COL_MAPPING['W'], COL_MAPPING['X']]] = self.df[[COL_MAPPING['W'], COL_MAPPING['X']]].fillna(0)

        # 计算费用金额
        self.df[COL_MAPPING['AG']] = (self.df[COL_MAPPING['W']] - self.df[COL_MAPPING['X']]) / 10000
        self.logger.info(f"已计算 {COL_MAPPING['AG']} 列。")

    def _process_expense_categories(self):
        """处理AH列（预算明细项）映射"""
        # 初始化AH列
        if COL_MAPPING['AH'] not in self.df.columns:
            self.df[COL_MAPPING['AH']] = None

        # 财务费用
        mask = self.df[COL_MAPPING['L']].str.contains('财务费用', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '财务费用'

        # 安全生产-其他
        mask = self.df[COL_MAPPING['L']].str.contains('安全生产-其他', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '安全监察费'

        # 办公费-办公用品
        mask = self.df[COL_MAPPING['L']].str.contains('办公费-办公用品', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '办公用品'

        # 办公费-其他行政费
        mask = self.df[COL_MAPPING['L']].str.contains('办公费-其他行政费', na=False)
        mask_monitor = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监控', na=False)
        mask_other = mask & (~mask_monitor)
        self.df.loc[mask_monitor, COL_MAPPING['AH']] = '监控费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '其他行政费用'

        # 办公费-邮寄费
        mask = self.df[COL_MAPPING['L']].str.contains('办公费-邮寄费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '邮寄费'

        # 财产保险费-财产一切险
        mask = self.df[COL_MAPPING['L']].str.contains('财产保险费-财产一切险', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '财产一切险'

        # 财产保险费-公众责任险
        mask = self.df[COL_MAPPING['L']].str.contains('财产保险费-公众责任险', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '公众责任险'

        # 差旅费
        mask = self.df[COL_MAPPING['L']].str.contains('差旅费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '差旅费'

        # 低值易耗品-办公用具
        mask = self.df[COL_MAPPING['L']].str.contains('低值易耗品-办公用具', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '办公用具'

        # 低值易耗品-消防器材
        mask = self.df[COL_MAPPING['L']].str.contains('低值易耗品-消防器材', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '消防器材'

        # 低值易耗品-运营工具
        mask = self.df[COL_MAPPING['L']].str.contains('低值易耗品-运营工具', na=False)
        mask_cost = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('翻新|除锈', na=False)
        mask_other = mask & (~mask_cost)
        self.df.loc[mask_cost, COL_MAPPING['AH']] = '成本'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '运营工具'

        # 服务费-售后服务费-维修费
        mask = self.df[COL_MAPPING['L']].str.contains('服务费-售后服务费-维修费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '售后服务费'

        # 福利费
        mask = self.df[COL_MAPPING['L']].str.contains('福利费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '福利费'

        # 工资-奖金
        mask = self.df[COL_MAPPING['L']].str.contains('工资-奖金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '工资'

        # 工资-销售提成-用户开发提成
        mask = self.df[COL_MAPPING['L']].str.contains('工资-销售提成-用户开发提成', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '销售提成'

        # 工资-员工工资-基本工资
        mask = self.df[COL_MAPPING['L']].str.contains('工资-员工工资-基本工资', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '工资'

        # 劳动保护费-工装费
        mask = self.df[COL_MAPPING['L']].str.contains('劳动保护费-工装费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '工服'

        # 劳动保护费-其他劳保用品
        mask = self.df[COL_MAPPING['L']].str.contains('劳动保护费-其他劳保用品', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '其他劳保用品'

        # 劳动保险费
        mask = self.df[COL_MAPPING['L']].str.contains('劳动保险费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '劳动保险费'

        # 劳务费
        mask = self.df[COL_MAPPING['L']].str.contains('劳务费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '工资'

        # 设备检测费-其他
        mask = self.df[COL_MAPPING['L']].str.contains('设备检测费-其他', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '质量检测费'

        # 社会保险金-工伤保险金
        mask = self.df[COL_MAPPING['L']].str.contains('社会保险金-工伤保险金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '五险一金'

        # 社会保险金-基本养老金
        mask = self.df[COL_MAPPING['L']].str.contains('社会保险金-基本养老金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '五险一金'

        # 社会保险金-生育保险金
        mask = self.df[COL_MAPPING['L']].str.contains('社会保险金-生育保险金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '五险一金'

        # 社会保险金-失业保险金
        mask = self.df[COL_MAPPING['L']].str.contains('社会保险金-失业保险金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '五险一金'

        # 社会保险金-医疗保险金
        mask = self.df[COL_MAPPING['L']].str.contains('社会保险金-医疗保险金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '五险一金'

        # 市内交通费
        mask = self.df[COL_MAPPING['L']].str.contains('市内交通费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '市内交通费'

        # 水电暖管理费-电费
        mask = self.df[COL_MAPPING['L']].str.contains('水电暖管理费-电费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '电费'

        # 水电暖管理费-取暖费
        mask = self.df[COL_MAPPING['L']].str.contains('水电暖管理费-取暖费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '取暖费'

        # 水电暖管理费-水费
        mask = self.df[COL_MAPPING['L']].str.contains('水电暖管理费-水费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '水费'

        # 水电暖管理费-物业管理费
        mask = self.df[COL_MAPPING['L']].str.contains('水电暖管理费-物业管理费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '物业管理费'

        # 通讯费-网络租金
        mask = self.df[COL_MAPPING['L']].str.contains('通讯费-网络租金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '网络租金'

        # 通讯费-系统分摊费
        mask = self.df[COL_MAPPING['L']].str.contains('通讯费-系统分摊费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '系统分摊费'

        # 通讯费-系统服务费
        mask = self.df[COL_MAPPING['L']].str.contains('通讯费-系统服务费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '系统服务费'

        # 通讯费-移动话费
        mask = self.df[COL_MAPPING['L']].str.contains('通讯费-移动话费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '移动话费'

        # 维修费-办公行政维修
        mask = self.df[COL_MAPPING['L']].str.contains('维修费-办公行政维修', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '办公行政维修'

        # 无形资产摊销
        mask = self.df[COL_MAPPING['L']].str.contains('无形资产摊销', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '无形资产'

        # 物流费用-运杂费-货运费用
        mask = self.df[COL_MAPPING['L']].str.contains('物流费用-运杂费-货运费用', na=False)
        mask_cost = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('支线', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('装卸|卸车|装车|吊车|叉车|劳务|人工|人力|搬运', na=False)
        mask_moving = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('搬迁|搬仓', na=False)
        mask_delivery = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('调剂|运输|物流|运费|配送', na=False)
        mask_other = mask & (~mask_cost) & (~mask_loading) & (~mask_moving) & (~mask_delivery)
        self.df.loc[mask_cost, COL_MAPPING['AH']] = '成本'
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_moving, COL_MAPPING['AH']] = '搬仓费'
        self.df.loc[mask_delivery, COL_MAPPING['AH']] = '调剂费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '调剂费'

        # 物流费用-运杂费-其他费用
        mask = self.df[COL_MAPPING['L']].str.contains('物流费用-运杂费-其他费用', na=False)
        mask_cost = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('支线', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('装卸|卸车|装车|吊车|叉车|劳务|人工|人力|搬运', na=False)
        mask_moving = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('搬迁|搬仓', na=False)
        mask_delivery = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('调剂|运输|物流|运费|配送', na=False)
        mask_other = mask & (~mask_cost) & (~mask_loading) & (~mask_moving) & (~mask_delivery)
        self.df.loc[mask_cost, COL_MAPPING['AH']] = '成本'
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_moving, COL_MAPPING['AH']] = '搬仓费'
        self.df.loc[mask_delivery, COL_MAPPING['AH']] = '调剂费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '搬仓费'

        # 物流费用-运杂费-装卸及过磅费
        mask = self.df[COL_MAPPING['L']].str.contains('物流费用-运杂费-装卸及过磅费', na=False)
        mask_cost = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('支线', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('装卸|卸车|装车|吊车|叉车|劳务|人工|人力|搬运', na=False)
        mask_moving = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('搬迁|搬仓', na=False)
        mask_delivery = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('调剂|运输|物流|运费|配送', na=False)
        mask_other = mask & (~mask_cost) & (~mask_loading) & (~mask_moving) & (~mask_delivery)
        self.df.loc[mask_cost, COL_MAPPING['AH']] = '成本'
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_moving, COL_MAPPING['AH']] = '搬仓费'
        self.df.loc[mask_delivery, COL_MAPPING['AH']] = '调剂费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '装卸费'

        # 业务招待费
        mask = self.df[COL_MAPPING['L']].str.contains('业务招待费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '业务招待费'

        # 长期待摊费用摊销
        mask = self.df[COL_MAPPING['L']].str.contains('长期待摊费用摊销', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '长期待摊'

        # 折旧
        mask = self.df[COL_MAPPING['L']].str.contains('折旧', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '折旧'

        # 职工教育经费
        mask = self.df[COL_MAPPING['L']].str.contains('职工教育经费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '职工教育经费'

        # 中介服务费-法律事务费
        mask = self.df[COL_MAPPING['L']].str.contains('中介服务费-法律事务费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '法律事务费'

        # 中介服务费-审计
        mask = self.df[COL_MAPPING['L']].str.contains('中介服务费-审计', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '审计、评估'

        # 中介服务费-咨询费
        mask = self.df[COL_MAPPING['L']].str.contains('中介服务费-咨询费', na=False)
        mask_monitor = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监造', na=False)
        mask_other = mask & (~mask_monitor)
        self.df.loc[mask_monitor, COL_MAPPING['AH']] = '监造费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '咨询费'

        # 住房公积金
        mask = self.df[COL_MAPPING['L']].str.contains('住房公积金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '五险一金'

        # 租赁费-办公场地租赁
        mask = self.df[COL_MAPPING['L']].str.contains('租赁费-办公场地租赁', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '办公场地租赁'

        # 租赁费-员工宿舍租赁
        mask = self.df[COL_MAPPING['L']].str.contains('租赁费-员工宿舍租赁', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '宿舍费'

        self.logger.info(f"已完成 {COL_MAPPING['AH']} 列的条件判别。")


    def _process_ai_column(self):
        """处理AI列（运营费用大类）映射"""
        # 基于AH列的值进行映射
        ai_mapping = {
            '安全监察费': '质检售后安监费',
            '搬仓费': '装卸调剂搬仓',
            '办公场地租赁': '仓租费',
            '办公行政维修': '维修费',
            '办公用具': '低值易耗品',
            '办公用品': '办公费',
            '财产一切险': '商业保险',
            '财务费用': '不分析',
            '差旅费': '差旅费',
            '成本': '不分析',
            '电费': '水电暖物业费',
            '调剂费': '装卸调剂搬仓',
            '法律事务费': '中介服务费',
            '福利费': '人力成本',
            '工服': '劳动保护费',
            '工资': '人力成本',
            '公众责任险': '商业保险',
            '监控费': '办公费',
            '监造费': '中介服务费',
            '劳动保险费': '商业保险',
            '其他劳保用品': '劳动保护费',
            '其他行政费用': '办公费',
            '取暖费': '不分析',
            '审计、评估': '中介服务费',
            '市内交通费': '差旅费',
            '售后服务费': '质检售后安监费',
            '水费': '水电暖物业费',
            '宿舍费': '不分析',
            '网络租金': '通讯费',
            '无形资产': '不分析',
            '五险一金': '人力成本',
            '物业管理费': '水电暖物业费',
            '系统分摊费': '通讯费',
            '系统服务费': '通讯费',
            '消防器材': '低值易耗品',
            '销售提成': '人力成本',
            '业务招待费': '招待费',
            '移动话费': '移动话费',
            '邮寄费': '办公费',
            '运营工具': '低值易耗品',
            '长期待摊': '不分析',
            '折旧': '不分析',
            '职工教育经费': '人力成本',
            '质量检测费': '质检售后安监费',
            '装卸费': '装卸调剂搬仓',
            '咨询费': '中介服务费'
        }

        for ah_value, ai_value in ai_mapping.items():
            mask = self.df[COL_MAPPING['AH']] == ah_value
            self.df.loc[mask, COL_MAPPING['AI']] = ai_value

        self.logger.info(f"已完成 {COL_MAPPING['AI']} 列的映射。")

    def _process_aj_column(self):
        """处理AJ列（部门）映射"""
        # 基于N列的值进行映射
        for n_value, aj_value in AJ_MAPPING.items():
            mask = self.df[COL_MAPPING['N']] == n_value
            self.df.loc[mask, COL_MAPPING['AJ']] = aj_value

        self.logger.info(f"已完成 {COL_MAPPING['AJ']} 列的映射。")

    def _process_ak_column(self):
        """处理AK列（部门类别）映射"""
        # 基于AJ列的值进行映射
        for aj_value, ak_value in AK_MAPPING.items():
            mask = self.df[COL_MAPPING['AJ']] == aj_value
            self.df.loc[mask, COL_MAPPING['AK']] = ak_value

        self.logger.info(f"已完成 {COL_MAPPING['AK']} 列的映射。")

    def _process_al_column(self):
        """处理AL列（是否预算）判别逻辑"""
        # 1. 若 AH 列为"财务费用"、"宿舍费"、"取暖费"，则 AL 列为"N"
        mask1 = self.df[COL_MAPPING['AH']].isin(['财务费用', '宿舍费', '取暖费'])
        self.df.loc[mask1, COL_MAPPING['AL']] = 'N'

        # 2. 若 AH 列不是"折旧"、"长期待摊"、"无形资产"，且 AK 列为"集团"，则 AL 列为"N"
        mask2 = (~self.df[COL_MAPPING['AH']].isin(['折旧', '长期待摊', '无形资产'])) & (self.df[COL_MAPPING['AK']] == '集团')
        self.df.loc[mask2, COL_MAPPING['AL']] = 'N'

        # 3. 其余情况，AL 列为"Y"
        mask3 = ~(mask1 | mask2)
        self.df.loc[mask3, COL_MAPPING['AL']] = 'Y'

        self.logger.info(f"已完成 {COL_MAPPING['AL']} 列的判别。")

    def _process_am_column(self):
        """处理AM列（是否可控）判别逻辑"""
        # 1. 若 AL 列为"N"，则 AM 列为"N"
        mask1 = self.df[COL_MAPPING['AL']] == 'N'
        self.df.loc[mask1, COL_MAPPING['AM']] = 'N'

        # 2. 若 AK 列为"集团"，则 AM 列为"N"
        mask2 = self.df[COL_MAPPING['AK']] == '集团'
        self.df.loc[mask2, COL_MAPPING['AM']] = 'N'

        # 3. 若 AH 列为"折旧"或"长期待摊"或"无形资产"，则 AM 列为"N"
        mask3 = self.df[COL_MAPPING['AH']].isin(['折旧', '长期待摊', '无形资产'])
        self.df.loc[mask3, COL_MAPPING['AM']] = 'N'

        # 4. 其余情况，AM 列为"Y"
        mask4 = ~(mask1 | mask2 | mask3)
        self.df.loc[mask4, COL_MAPPING['AM']] = 'Y'

        self.logger.info(f"已完成 {COL_MAPPING['AM']} 列的判别。")

    def _process_an_column(self):
        """处理AN列（是否一盘货）判别逻辑"""
        # 若 Z 列包含"一盘货"字眼，则 AN 列为"Y"，否则为"N"
        mask_yes = self.df[COL_MAPPING['Z']].astype(str).str.contains('一盘货', na=False)
        mask_no = ~mask_yes

        self.df.loc[mask_yes, COL_MAPPING['AN']] = 'Y'
        self.df.loc[mask_no, COL_MAPPING['AN']] = 'N'

        self.logger.info(f"已完成 {COL_MAPPING['AN']} 列的判别。")

    def get_output_file(self):
        """获取输出文件路径"""
        return self.output_file