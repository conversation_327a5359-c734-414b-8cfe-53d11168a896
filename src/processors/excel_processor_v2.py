"""
Excel数据处理器核心模块（版本2），使用列序号而不是列名
"""
import pandas as pd
from src.utils.config import COLUMN_LETTERS, NEW_COLUMN_HEADERS, ALLOWED_L_SUBSTRINGS
from src.utils.helpers import get_output_filename
from src.utils.department_mapping import AJ_MAPPING, AK_MAPPING

class ExcelProcessorV2:
    """
    Excel数据处理器类（版本2），负责处理Excel文件中的财务数据
    使用列序号而不是列名，避免因列名变化导致的问题
    """

    def __init__(self, logger):
        """
        初始化Excel处理器

        参数:
            logger: 日志对象，用于记录处理过程
        """
        self.logger = logger
        self.df = None
        self.input_file = None
        self.output_file = None

    def _get_column_by_letter(self, letter):
        """
        根据Excel列字母获取pandas列索引

        参数:
            letter: Excel列字母，如'L', 'N'等

        返回:
            列索引或列名
        """
        if letter in COLUMN_LETTERS:
            col_index = COLUMN_LETTERS[letter]
            if col_index < len(self.df.columns):
                return self.df.columns[col_index]
            else:
                # 如果列不存在，返回None
                return None
        return None

    def _create_column_by_letter(self, letter, header_name=None):
        """
        根据Excel列字母创建新列

        参数:
            letter: Excel列字母，如'AG', 'AH'等
            header_name: 列头名称，如果不提供则使用默认名称
        """
        if header_name is None:
            header_name = NEW_COLUMN_HEADERS.get(letter, f'新列{letter}')

        # 如果列不存在，创建它
        if header_name not in self.df.columns:
            self.df[header_name] = ''  # 使用空字符串而不是None，避免类型转换问题

        return header_name

    def process_file(self, input_file, custom_output_file=None):
        """
        处理Excel文件的主函数

        参数:
            input_file: 输入文件路径
            custom_output_file: 自定义输出文件路径（可选）

        返回:
            布尔值，表示处理是否成功
        """
        try:
            self.input_file = input_file

            # 设置输出文件路径
            if custom_output_file:
                self.output_file = custom_output_file
            else:
                self.output_file = get_output_filename(input_file)

            self.logger.info(f"开始处理文件: {self.input_file}")

            # 读取Excel文件
            self.df = pd.read_excel(self.input_file)
            self.logger.info(f"成功读取 {len(self.df)} 行数据。")

            # 检查必要的列是否存在
            self._validate_columns()

            # 创建新列用于后续处理
            self._create_new_columns()

            # 数据清洗和筛选
            self._clean_and_filter_data()

            if self.df.empty:
                self.logger.warning("筛选后数据为空，处理终止。")
                return False

            # 计算费用金额
            self._calculate_expense_amount()

            # 处理费用项目映射
            self._process_expense_categories()

            # 处理AI列（运营费用大类）
            self._process_ai_column()

            # 处理AJ列（部门）
            self._process_aj_column()

            # 处理AK列（部门类别）
            self._process_ak_column()

            # 处理AL列（是否预算）
            self._process_al_column()

            # 处理AM列（是否可控）
            self._process_am_column()

            # 处理AN列（是否一盘货）
            self._process_an_column()

            # 保存结果
            self.df.to_excel(self.output_file, index=False, engine='openpyxl')
            self.logger.info(f"处理完成！结果已保存到: {self.output_file}")

            return True

        except FileNotFoundError:
            self.logger.error(f"错误：输入文件 {input_file} 未找到。请检查文件路径和名称。")
            return False
        except KeyError as e:
            self.logger.error(f"错误：映射的列名不存在: {e}。请检查列名映射。")
            return False
        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {e}")
            return False

    def _validate_columns(self):
        """验证必要的列是否存在"""
        required_letters = ['L', 'N', 'W', 'X', 'Z']
        missing_columns = []

        for letter in required_letters:
            col_name = self._get_column_by_letter(letter)
            if col_name is None:
                missing_columns.append(f"{letter}列(第{COLUMN_LETTERS[letter]+1}列)")

        if missing_columns:
            self.logger.warning(f"警告: 以下列不存在或超出范围: {', '.join(missing_columns)}")
        else:
            self.logger.info("列验证完成")

    def _create_new_columns(self):
        """创建新列用于后续处理"""
        new_letters = ['AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN']

        for letter in new_letters:
            self._create_column_by_letter(letter)

        self.logger.info("已创建所需的新列")

    def _clean_and_filter_data(self):
        """数据清洗和筛选"""
        # 记录原始行数
        original_rows = len(self.df)

        # 1.1 删除 N 列为空数据的行
        n_col = self._get_column_by_letter('N')
        if n_col:
            self.df.dropna(subset=[n_col], inplace=True)
            self.logger.info(f"根据 N列({n_col}) 删除空值后剩余 {len(self.df)} 行 (删除了 {original_rows - len(self.df)} 行)。")

        # 1.2 L列保留包含指定费用类型的行，其余删除
        original_rows = len(self.df)
        l_col = self._get_column_by_letter('L')

        if l_col:
            # 创建筛选条件：包含任一指定字段
            filter_condition = self.df[l_col].astype(str).str.contains(ALLOWED_L_SUBSTRINGS[0], na=False)
            for substring in ALLOWED_L_SUBSTRINGS[1:]:
                filter_condition = filter_condition | self.df[l_col].astype(str).str.contains(substring, na=False)

            self.df = self.df[filter_condition]
            self.logger.info(f"根据 L列({l_col}) 筛选后剩余 {len(self.df)} 行 (删除了 {original_rows - len(self.df)} 行)。")

    def _calculate_expense_amount(self):
        """计算 费用金额(万元) (AG列)"""
        w_col = self._get_column_by_letter('W')
        x_col = self._get_column_by_letter('X')
        ag_col = self._create_column_by_letter('AG')

        if w_col and x_col:
            # 确保 W 和 X 列是数值类型，非数值转为NaN，然后填充0
            self.df[w_col] = pd.to_numeric(self.df[w_col], errors='coerce')
            self.df[x_col] = pd.to_numeric(self.df[x_col], errors='coerce')
            # 如果转换后出现NaN (比如原单元格是文本)，填充0
            self.df[[w_col, x_col]] = self.df[[w_col, x_col]].fillna(0)

            # 计算费用金额
            self.df[ag_col] = (self.df[w_col] - self.df[x_col]) / 10000
            self.logger.info(f"已计算 {ag_col} 列。")
        else:
            self.logger.warning("W列或X列不存在，无法计算费用金额")

    def _process_expense_categories(self):
        """处理AH列（预算明细项）映射"""
        l_col = self._get_column_by_letter('L')
        z_col = self._get_column_by_letter('Z')
        ah_col = self._create_column_by_letter('AH')

        if not l_col:
            self.logger.warning("L列不存在，无法处理费用项目映射")
            return

        # 财务费用
        mask = self.df[l_col].str.contains('财务费用', na=False)
        self.df.loc[mask, ah_col] = '财务费用'

        # 安全生产-其他
        mask = self.df[l_col].str.contains('安全生产-其他', na=False)
        self.df.loc[mask, ah_col] = '安全监察费'

        # 办公费-办公用品
        mask = self.df[l_col].str.contains('办公费-办公用品', na=False)
        self.df.loc[mask, ah_col] = '办公用品'

        # 办公费-其他行政费
        mask = self.df[l_col].str.contains('办公费-其他行政费', na=False)
        if z_col:
            mask_monitor = mask & self.df[z_col].astype(str).str.contains('监控', na=False)
            mask_other = mask & (~mask_monitor)
            self.df.loc[mask_monitor, ah_col] = '监控费'
            self.df.loc[mask_other, ah_col] = '其他行政费用'
        else:
            self.df.loc[mask, ah_col] = '其他行政费用'

        # 办公费-邮寄费
        mask = self.df[l_col].str.contains('办公费-邮寄费', na=False)
        self.df.loc[mask, ah_col] = '邮寄费'

        # 财产保险费-财产一切险
        mask = self.df[l_col].str.contains('财产保险费-财产一切险', na=False)
        self.df.loc[mask, ah_col] = '财产一切险'

        # 财产保险费-公众责任险
        mask = self.df[l_col].str.contains('财产保险费-公众责任险', na=False)
        self.df.loc[mask, ah_col] = '公众责任险'

        # 差旅费
        mask = self.df[l_col].str.contains('差旅费', na=False)
        self.df.loc[mask, ah_col] = '差旅费'

        # 低值易耗品-办公用具
        mask = self.df[l_col].str.contains('低值易耗品-办公用具', na=False)
        self.df.loc[mask, ah_col] = '办公用具'

        # 低值易耗品-消防器材
        mask = self.df[l_col].str.contains('低值易耗品-消防器材', na=False)
        self.df.loc[mask, ah_col] = '消防器材'

        # 低值易耗品-运营工具
        mask = self.df[l_col].str.contains('低值易耗品-运营工具', na=False)
        if z_col:
            mask_cost = mask & self.df[z_col].astype(str).str.contains('翻新|除锈', na=False)
            mask_other = mask & (~mask_cost)
            self.df.loc[mask_cost, ah_col] = '成本'
            self.df.loc[mask_other, ah_col] = '运营工具'
        else:
            self.df.loc[mask, ah_col] = '运营工具'

        self.logger.info(f"已完成 {ah_col} 列的条件判别（第一部分）。")

    def _process_ai_column(self):
        """处理AI列（运营费用大类）映射"""
        ah_col = self._get_column_by_letter('AH') or self._create_column_by_letter('AH')
        ai_col = self._create_column_by_letter('AI')

        # 简化的AI映射（只包含几个主要的）
        ai_mapping = {
            '财务费用': '不分析',
            '办公用品': '办公费',
            '差旅费': '差旅费',
            '五险一金': '人力成本',
            '工资': '人力成本'
        }

        # 先执行具体映射
        for ah_value, ai_value in ai_mapping.items():
            mask = self.df[ah_col] == ah_value
            self.df.loc[mask, ai_col] = ai_value

        # 为所有没有映射的AH值设置默认值
        mask_unmapped = (self.df[ai_col] == '') & self.df[ah_col].notna() & (self.df[ah_col] != '')
        self.df.loc[mask_unmapped, ai_col] = '其他'

        self.logger.info(f"已完成 {ai_col} 列的映射。")

    def _process_aj_column(self):
        """处理AJ列（部门）映射"""
        n_col = self._get_column_by_letter('N')
        aj_col = self._create_column_by_letter('AJ')

        if not n_col:
            self.logger.warning("N列不存在，无法处理部门映射")
            return

        # 使用部门映射
        for n_value, aj_value in AJ_MAPPING.items():
            mask = self.df[n_col] == n_value
            self.df.loc[mask, aj_col] = aj_value

        self.logger.info(f"已完成 {aj_col} 列的映射。")

    def _process_ak_column(self):
        """处理AK列（部门类别）映射"""
        aj_col = self._get_column_by_letter('AJ') or self._create_column_by_letter('AJ')
        ak_col = self._create_column_by_letter('AK')

        # 使用部门类别映射
        for aj_value, ak_value in AK_MAPPING.items():
            mask = self.df[aj_col] == aj_value
            self.df.loc[mask, ak_col] = ak_value

        # 为所有没有映射的AJ值设置默认值
        mask_unmapped = (self.df[ak_col] == '') & self.df[aj_col].notna() & (self.df[aj_col] != '')
        self.df.loc[mask_unmapped, ak_col] = '其他'

        self.logger.info(f"已完成 {ak_col} 列的映射。")

    def _process_al_column(self):
        """处理AL列（是否预算）判别逻辑"""
        ah_col = self._get_column_by_letter('AH') or self._create_column_by_letter('AH')
        ak_col = self._get_column_by_letter('AK') or self._create_column_by_letter('AK')
        al_col = self._create_column_by_letter('AL')

        # 1. 若 AH 列为"财务费用"、"宿舍费"、"取暖费"，则 AL 列为"N"
        mask1 = self.df[ah_col].isin(['财务费用', '宿舍费', '取暖费'])
        self.df.loc[mask1, al_col] = 'N'

        # 2. 若 AH 列不是"折旧"、"长期待摊"、"无形资产"，且 AK 列为"集团"，则 AL 列为"N"
        mask2 = (~self.df[ah_col].isin(['折旧', '长期待摊', '无形资产'])) & (self.df[ak_col] == '集团')
        self.df.loc[mask2, al_col] = 'N'

        # 3. 其余情况，AL 列为"Y"
        mask3 = ~(mask1 | mask2)
        self.df.loc[mask3, al_col] = 'Y'

        self.logger.info(f"已完成 {al_col} 列的判别。")

    def _process_am_column(self):
        """处理AM列（是否可控）判别逻辑"""
        ah_col = self._get_column_by_letter('AH') or self._create_column_by_letter('AH')
        ak_col = self._get_column_by_letter('AK') or self._create_column_by_letter('AK')
        al_col = self._get_column_by_letter('AL') or self._create_column_by_letter('AL')
        am_col = self._create_column_by_letter('AM')

        # 1. 若 AL 列为"N"，则 AM 列为"N"
        mask1 = self.df[al_col] == 'N'
        self.df.loc[mask1, am_col] = 'N'

        # 2. 若 AK 列为"集团"，则 AM 列为"N"
        mask2 = self.df[ak_col] == '集团'
        self.df.loc[mask2, am_col] = 'N'

        # 3. 若 AH 列为"折旧"或"长期待摊"或"无形资产"，则 AM 列为"N"
        mask3 = self.df[ah_col].isin(['折旧', '长期待摊', '无形资产'])
        self.df.loc[mask3, am_col] = 'N'

        # 4. 其余情况，AM 列为"Y"
        mask4 = ~(mask1 | mask2 | mask3)
        self.df.loc[mask4, am_col] = 'Y'

        self.logger.info(f"已完成 {am_col} 列的判别。")

    def _process_an_column(self):
        """处理AN列（是否一盘货）判别逻辑"""
        z_col = self._get_column_by_letter('Z')
        an_col = self._create_column_by_letter('AN')

        if not z_col:
            self.logger.warning("Z列不存在，无法处理一盘货判别")
            # 默认设置为N
            self.df[an_col] = 'N'
            return

        # 若 Z 列包含"一盘货"字眼，则 AN 列为"Y"，否则为"N"
        mask_yes = self.df[z_col].astype(str).str.contains('一盘货', na=False)
        mask_no = ~mask_yes

        self.df.loc[mask_yes, an_col] = 'Y'
        self.df.loc[mask_no, an_col] = 'N'

        self.logger.info(f"已完成 {an_col} 列的判别。")

    def get_output_file(self):
        """获取输出文件路径"""
        return self.output_file
