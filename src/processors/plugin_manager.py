"""
插件管理器模块，用于支持可扩展的数据处理流程
"""
import os
import importlib.util
import inspect

class PluginManager:
    """
    插件管理器类，负责加载和管理数据处理插件
    """
    
    def __init__(self, logger):
        """
        初始化插件管理器
        
        参数:
            logger: 日志对象，用于记录插件加载过程
        """
        self.logger = logger
        self.plugins = {}
        
    def load_plugins(self, plugin_dir="plugins"):
        """
        从插件目录加载所有插件模块
        
        参数:
            plugin_dir: 插件目录路径
        """
        if not os.path.exists(plugin_dir):
            os.makedirs(plugin_dir)
            self.logger.info(f"已创建插件目录: {plugin_dir}")
            return
            
        plugin_files = [f for f in os.listdir(plugin_dir) 
                      if f.endswith('.py') and not f.startswith('__')]
                      
        for plugin_file in plugin_files:
            try:
                plugin_path = os.path.join(plugin_dir, plugin_file)
                plugin_name = os.path.splitext(plugin_file)[0]
                
                # 动态加载模块
                spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 查找插件类
                for item_name in dir(module):
                    item = getattr(module, item_name)
                    if (inspect.isclass(item) and 
                        hasattr(item, 'process') and 
                        callable(getattr(item, 'process'))):
                        
                        plugin_instance = item(self.logger)
                        self.plugins[plugin_name] = plugin_instance
                        self.logger.info(f"已加载插件: {plugin_name}")
                        break
                        
            except Exception as e:
                self.logger.error(f"加载插件 {plugin_file} 失败: {e}")
                
        self.logger.info(f"共加载了 {len(self.plugins)} 个插件")
        
    def get_plugin_names(self):
        """
        获取所有已加载的插件名称
        
        返回:
            插件名称列表
        """
        return list(self.plugins.keys())
        
    def get_plugin(self, plugin_name):
        """
        获取指定名称的插件
        
        参数:
            plugin_name: 插件名称
            
        返回:
            插件实例或None（如果插件不存在）
        """
        return self.plugins.get(plugin_name)
        
    def run_plugin(self, plugin_name, df):
        """
        运行指定的插件处理数据
        
        参数:
            plugin_name: 插件名称
            df: 数据框
            
        返回:
            处理后的数据框
        """
        plugin = self.get_plugin(plugin_name)
        if plugin:
            try:
                self.logger.info(f"正在运行插件: {plugin_name}")
                result_df = plugin.process(df)
                self.logger.info(f"插件 {plugin_name} 运行完成")
                return result_df
            except Exception as e:
                self.logger.error(f"运行插件 {plugin_name} 失败: {e}")
                return df
        else:
            self.logger.warning(f"插件 {plugin_name} 不存在")
            return df 