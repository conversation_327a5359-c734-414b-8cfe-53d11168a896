"""
日志工具模块，提供日志收集和展示功能
"""
import logging
from datetime import datetime
import os

class Logger:
    """
    日志管理类，支持文件记录和GUI显示
    """
    def __init__(self, log_widget=None):
        """
        初始化日志管理器
        
        参数:
            log_widget: GUI界面上的文本控件，用于显示日志信息
        """
        self.log_widget = log_widget
        self.logger = logging.getLogger("财务数据处理工具")
        self.logger.setLevel(logging.INFO)
        
        # 创建日志目录
        if not os.path.exists("logs"):
            os.makedirs("logs")
            
        # 设置文件处理器
        log_file = f"logs/finance_tool_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        
    def info(self, message):
        """记录信息级别日志"""
        self.logger.info(message)
        self._update_widget(message)
        
    def warning(self, message):
        """记录警告级别日志"""
        self.logger.warning(message)
        self._update_widget(f"警告: {message}")
        
    def error(self, message):
        """记录错误级别日志"""
        self.logger.error(message)
        self._update_widget(f"错误: {message}")
        
    def _update_widget(self, message):
        """更新GUI日志控件"""
        if self.log_widget:
            # 确保在主线程中更新UI
            try:
                self.log_widget.insert("end", f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
                self.log_widget.see("end")  # 自动滚动到最新日志
            except Exception as e:
                self.logger.error(f"更新日志控件失败: {e}")
                
    def set_log_widget(self, log_widget):
        """设置日志控件，用于动态更新GUI"""
        self.log_widget = log_widget 