"""
工具函数模块，提供通用功能
"""
import os
import pandas as pd
from datetime import datetime

def get_output_filename(input_file):
    """
    根据输入文件名生成输出文件名
    
    参数:
        input_file: 输入文件路径
        
    返回:
        输出文件路径
    """
    file_dir = os.path.dirname(input_file)
    file_name, file_ext = os.path.splitext(os.path.basename(input_file))
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(file_dir, f"{file_name}-处理过-{timestamp}{file_ext}")
    return output_file

def is_valid_excel(file_path):
    """
    检查文件是否为有效的Excel文件
    
    参数:
        file_path: 文件路径
        
    返回:
        布尔值，表示是否为有效的Excel文件
    """
    if not os.path.exists(file_path):
        return False
        
    _, ext = os.path.splitext(file_path)
    valid_exts = ['.xlsx', '.xls', '.xlsm']
    
    if ext.lower() not in valid_exts:
        return False
        
    # 尝试打开Excel文件
    try:
        pd.read_excel(file_path, nrows=1)
        return True
    except Exception:
        return False

def format_file_size(size_bytes):
    """
    格式化文件大小
    
    参数:
        size_bytes: 文件大小（字节）
        
    返回:
        格式化后的文件大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.2f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.2f} MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.2f} GB"

def get_file_info(file_path):
    """
    获取文件信息
    
    参数:
        file_path: 文件路径
        
    返回:
        包含文件信息的字典
    """
    if not os.path.exists(file_path):
        return None
        
    file_stats = os.stat(file_path)
    
    return {
        "name": os.path.basename(file_path),
        "path": file_path,
        "size": format_file_size(file_stats.st_size),
        "modified": datetime.fromtimestamp(file_stats.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
    } 