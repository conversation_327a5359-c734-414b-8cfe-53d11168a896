"""
应用程序入口点
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox

def check_dependencies():
    """检查依赖项是否已安装"""
    try:
        import pandas
        import openpyxl
        return True
    except ImportError as e:
        messagebox.showerror("错误", f"缺少必要的依赖项: {e}\n请确保已安装所有依赖项。")
        return False

def create_root_window():
    """创建并返回应用程序的根窗口"""
    # 首先尝试使用带主题的窗口
    try:
        from ttkthemes import ThemedTk
        root = ThemedTk(theme="arc")  # 使用arc主题
    except ImportError:
        # 如果无法导入ttkthemes，使用普通的Tk
        root = tk.Tk()
        
    # 然后尝试启用拖放功能
    try:
        # 尝试加载tkdnd
        from tkinterdnd2 import TkinterDnD, DND_ALL
        
        # 记住当前窗口的标题和大小
        title = root.title()
        geometry = root.geometry()
        
        # 关闭原始窗口
        root.destroy()
        
        # 创建新的DnD窗口
        dnd_root = TkinterDnD.Tk()
        dnd_root.title(title)
        dnd_root.geometry(geometry)
        
        return dnd_root
    except ImportError:
        # 如果无法加载tkdnd，继续使用普通窗口
        print("警告: 无法加载拖放功能支持库(tkinterdnd2)，拖放功能将不可用。")
        return root

def main():
    """应用程序主入口点"""
    # 检查必要的依赖项
    if not check_dependencies():
        sys.exit(1)
        
    # 创建根窗口，已包含主题和拖放支持
    root = create_root_window()
    
    # 创建并启动主窗口
    from src.gui.main_window import MainWindow
    app = MainWindow(root)
    app.start()

if __name__ == "__main__":
    main() 