"""
主窗口界面模块，包含GUI界面的主要实现
"""
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import sys
from pathlib import Path

from src.processors.excel_processor import ExcelProcessor
from src.processors.plugin_manager import PluginManager
from src.utils.logger import Logger
from src.utils.helpers import is_valid_excel, get_file_info
from src.utils.config import APP_NAME, APP_VERSION, WINDOW_WIDTH, WINDOW_HEIGHT

class MainWindow:
    """
    主窗口类，负责创建和管理GUI界面
    """
    
    def __init__(self, root):
        """
        初始化主窗口
        
        参数:
            root: tkinter根窗口对象
        """
        self.root = root
        self.root.title(f"{APP_NAME} v{APP_VERSION}")
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        
        # 可以在这里设置图标
        # self.root.iconbitmap("assets/icon.ico") 
        
        # 创建日志对象
        self.setup_logger()
        
        # 创建Excel处理器
        self.excel_processor = ExcelProcessor(self.logger)
        
        # 创建插件管理器
        self.plugin_manager = PluginManager(self.logger)
        self.plugin_manager.load_plugins()
        
        # 正在处理的标志
        self.processing = False
        
        # 设置界面布局
        self.setup_ui()
        
        # 设置文件拖放功能
        self.setup_drop_target()
        
    def setup_logger(self):
        """设置日志记录器"""
        self.logger = Logger()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架，使用网格布局
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 上半部分：文件选择和操作区域
        self.setup_top_section()
        
        # 下半部分：日志显示区域
        self.setup_bottom_section()
        
    def setup_top_section(self):
        """设置界面上半部分"""
        # 文件选择框架
        file_frame = ttk.LabelFrame(self.main_frame, text="文件选择", padding="10")
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件路径输入框
        self.file_path_var = tk.StringVar()
        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file).grid(row=0, column=2, padx=5, pady=5)
        
        # 文件信息框架
        info_frame = ttk.LabelFrame(self.main_frame, text="文件信息", padding="10")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件信息标签
        self.file_info_text = scrolledtext.ScrolledText(info_frame, height=4, width=70, wrap=tk.WORD)
        self.file_info_text.pack(fill=tk.X, expand=True, padx=5, pady=5)
        self.file_info_text.insert(tk.END, "拖放Excel文件到此处或点击'浏览...'按钮选择文件")
        self.file_info_text.config(state=tk.DISABLED)
        
        # 操作按钮框架
        btn_frame = ttk.Frame(self.main_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(btn_frame, orient=tk.HORIZONTAL, 
                                      length=100, mode='indeterminate',
                                      variable=self.progress_var)
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 处理按钮
        self.process_btn = ttk.Button(btn_frame, text="处理文件", command=self.process_file)
        self.process_btn.pack(side=tk.RIGHT, padx=5)
        
        # 查看结果按钮
        self.view_btn = ttk.Button(btn_frame, text="查看结果", command=self.view_result, state=tk.DISABLED)
        self.view_btn.pack(side=tk.RIGHT, padx=5)
        
    def setup_bottom_section(self):
        """设置界面下半部分"""
        # 日志框架
        log_frame = ttk.LabelFrame(self.main_frame, text="处理日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=70, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 将日志文本框设置到日志对象中
        self.logger.set_log_widget(self.log_text)
        
        # 底部状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=2)
        
        # 状态标签
        self.status_var = tk.StringVar()
        self.status_var.set(f"{APP_NAME} v{APP_VERSION} - 就绪")
        ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W).pack(fill=tk.X)
        
    def setup_drop_target(self):
        """设置文件拖放功能"""
        try:
            # 尝试注册文件拖放事件
            self.root.drop_target_register("DND_Files")
            self.root.dnd_bind('<<Drop>>', self.handle_drop)
            self.logger.info("已启用文件拖放功能")
        except Exception as e:
            # 如果注册失败，记录日志但不中断程序
            self.logger.warning(f"启用拖放功能失败: {e}")
            self.logger.info("请使用'浏览...'按钮选择文件")
            
    def handle_drop(self, event):
        """处理文件拖放事件"""
        try:
            file_path = event.data
            
            # 修正Windows下的文件路径格式
            if sys.platform == 'win32':
                file_path = file_path.replace('{', '').replace('}', '')
                
            if is_valid_excel(file_path):
                self.file_path_var.set(file_path)
                self.update_file_info(file_path)
                self.logger.info(f"已通过拖放加载文件: {file_path}")
            else:
                messagebox.showerror("错误", "请拖入有效的Excel文件(.xlsx, .xls, .xlsm)")
        except Exception as e:
            self.logger.error(f"处理拖放事件时发生错误: {e}")
            messagebox.showerror("错误", f"无法处理拖放的文件: {e}")
            
    def browse_file(self):
        """浏览并选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls *.xlsm"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.file_path_var.set(file_path)
            self.update_file_info(file_path)
            
    def update_file_info(self, file_path):
        """更新文件信息显示"""
        file_info = get_file_info(file_path)
        
        if file_info:
            self.file_info_text.config(state=tk.NORMAL)
            self.file_info_text.delete(1.0, tk.END)
            self.file_info_text.insert(tk.END, f"文件名: {file_info['name']}\n")
            self.file_info_text.insert(tk.END, f"路径: {file_info['path']}\n")
            self.file_info_text.insert(tk.END, f"大小: {file_info['size']}\n")
            self.file_info_text.insert(tk.END, f"修改时间: {file_info['modified']}")
            self.file_info_text.config(state=tk.DISABLED)
            
    def process_file(self):
        """处理Excel文件"""
        file_path = self.file_path_var.get()
        
        if not file_path:
            messagebox.showwarning("警告", "请先选择Excel文件")
            return
            
        if not is_valid_excel(file_path):
            messagebox.showerror("错误", "无效的Excel文件，请重新选择")
            return
            
        if self.processing:
            messagebox.showinfo("提示", "正在处理中，请稍候")
            return
            
        # 开始处理前的UI更新
        self.processing = True
        self.status_var.set("处理中...")
        self.process_btn.config(state=tk.DISABLED)
        self.view_btn.config(state=tk.DISABLED)
        self.progress.start(10)
        
        # 在新线程中处理文件，避免界面卡死
        threading.Thread(target=self._process_file_thread, args=(file_path,), daemon=True).start()
        
    def _process_file_thread(self, file_path):
        """在线程中处理文件"""
        try:
            # 处理文件
            success = self.excel_processor.process_file(file_path)
            
            # 处理完成后的UI更新
            self.root.after(0, self._update_ui_after_processing, success)
            
        except Exception as e:
            self.logger.error(f"处理文件时发生错误: {e}")
            self.root.after(0, self._update_ui_after_processing, False)
            
    def _update_ui_after_processing(self, success):
        """处理完成后更新UI"""
        self.processing = False
        self.progress.stop()
        self.process_btn.config(state=tk.NORMAL)
        
        if success:
            self.status_var.set("处理完成")
            self.view_btn.config(state=tk.NORMAL)
            messagebox.showinfo("成功", f"文件处理成功，结果已保存到:\n{self.excel_processor.get_output_file()}")
        else:
            self.status_var.set("处理失败")
            self.view_btn.config(state=tk.DISABLED)
            
    def view_result(self):
        """查看处理结果"""
        output_file = self.excel_processor.get_output_file()
        
        if not output_file or not os.path.exists(output_file):
            messagebox.showerror("错误", "结果文件不存在")
            return
            
        # 尝试用系统默认程序打开文件
        try:
            os.startfile(output_file) if sys.platform == 'win32' else os.system(f"open '{output_file}'")
        except Exception as e:
            self.logger.error(f"打开结果文件失败: {e}")
            messagebox.showerror("错误", f"无法打开结果文件: {e}")
            
    def start(self):
        """启动主窗口"""
        self.logger.info(f"{APP_NAME} v{APP_VERSION} 启动")
        self.root.mainloop() 