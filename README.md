# 财务数据处理工具

## 简介
本工具用于处理Excel财务数据，根据特定规则筛选和转换数据。它提供了一个简单易用的图形界面，允许用户通过拖放或浏览方式选择Excel文件进行处理。

## 主要功能
- 加载Excel文件并识别必要的列
- 根据特定规则清洗和筛选数据
- 计算费用金额(万元)
- 根据复杂条件规则处理费用项目分类
- 生成处理后的Excel文件
- 日志记录整个处理过程

## 使用方法
1. 双击运行程序
2. 通过"浏览..."按钮选择Excel文件，或直接将文件拖到窗口中
3. 点击"处理文件"按钮开始处理
4. 处理完成后，点击"查看结果"按钮查看生成的文件

## 系统要求
- Windows 7及以上操作系统
- 不需要额外安装Python或其他软件

## 技术支持
如有问题或建议，请联系技术支持。

## 版本历史
- v1.0.0 (2025-04-01): 初始版本，实现基本功能

## 扩展功能
本工具支持通过插件机制扩展功能。将符合规范的Python插件文件放置在plugins目录中，重启程序即可自动加载。 