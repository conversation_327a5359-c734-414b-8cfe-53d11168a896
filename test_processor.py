#!/usr/bin/env python3
"""
测试Excel处理器的脚本
"""
import pandas as pd
import logging
from src.processors.excel_processor import ExcelProcessor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    test_data = {
        '科目名称': [
            '财务费用',
            '管理费用-办公费-办公用品',
            '管理费用-差旅费',
            '管理费用-社会保险金-基本养老金',
            '物流费用-运杂费-货运费用'
        ],
        '部门段': [
            '总经办',
            '质量技术中心',
            '中原区域运营中心',
            '财务部',
            '济南中心仓'
        ],
        '原币借方': [10000, 5000, 3000, 8000, 12000],
        '原币贷方': [0, 0, 0, 0, 2000],
        '行说明': [
            '银行利息',
            '办公用品采购',
            '出差费用',
            '员工养老保险',
            '一盘货运输费用'
        ]
    }
    
    return pd.DataFrame(test_data)

def main():
    """主函数"""
    logger.info("开始测试Excel处理器...")
    
    # 创建测试数据
    test_df = create_test_data()
    logger.info(f"创建了 {len(test_df)} 行测试数据")
    
    # 保存测试数据到Excel文件
    test_file = 'test_input.xlsx'
    test_df.to_excel(test_file, index=False)
    logger.info(f"测试数据已保存到 {test_file}")
    
    # 创建处理器并处理数据
    processor = ExcelProcessor(logger)
    
    try:
        success = processor.process_file(test_file, 'test_output.xlsx')
        if success:
            logger.info("处理成功！")
            
            # 读取并显示结果
            result_df = pd.read_excel('test_output.xlsx')
            logger.info("处理结果:")
            print("\n" + "="*80)
            print(result_df.to_string(index=False))
            print("="*80)
            
        else:
            logger.error("处理失败！")
            
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
