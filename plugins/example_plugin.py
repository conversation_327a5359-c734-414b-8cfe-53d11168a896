"""
示例插件 - 展示如何扩展数据处理功能
"""

class ExamplePlugin:
    """
    示例插件类，展示如何扩展数据处理功能
    """
    
    def __init__(self, logger):
        """
        初始化插件
        
        参数:
            logger: 日志对象，用于记录处理过程
        """
        self.logger = logger
        self.logger.info("示例插件已初始化")
        
    def process(self, df):
        """
        处理数据框
        
        参数:
            df: 输入的pandas数据框
            
        返回:
            处理后的数据框
        """
        self.logger.info("示例插件正在处理数据...")
        
        # 示例：为数据框添加一个新列
        if not df.empty:
            # 添加一个"处理时间"列，记录数据处理的时间戳
            from datetime import datetime
            df['处理时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            self.logger.info(f"示例插件已处理 {len(df)} 行数据")
        else:
            self.logger.warning("数据框为空，无法处理")
            
        return df 