# 如何使用 GitHub Actions 构建 Windows 可执行文件

本项目使用 GitHub Actions 自动构建 Windows 可执行文件。这样您可以在 macOS 环境下开发，但通过 GitHub 提供 Windows 版本的应用程序给您的朋友或同事。

## 配置说明

已经创建了 `.github/workflows/build-windows.yml` 文件，该文件配置了一个自动构建流水线，可以在以下情况下触发：

1. 当您向 `main` 分支推送代码时
2. 当有人创建针对 `main` 分支的拉取请求时
3. 当您手动触发工作流时

## 使用方法

### 将代码上传到 GitHub

1. 在 GitHub 上创建一个新仓库
2. 将您的代码推送到该仓库：

```bash
git init
git add .
git commit -m "初始提交"
git branch -M main
git remote add origin https://github.com/您的用户名/您的仓库名.git
git push -u origin main
```

### 手动触发构建

1. 在 GitHub 仓库页面中，点击 "Actions" 标签
2. 从左侧菜单中选择 "构建 Windows 可执行文件" 工作流
3. 点击 "Run workflow" 按钮，再次点击 "Run workflow" 确认

### 下载构建结果

1. 构建完成后（约需 3-5 分钟），在 "Actions" 页面中找到最新的构建任务
2. 点击该任务，滚动到页面底部的 "Artifacts" 部分
3. 点击 "FinanceDataProcessor-Windows" 下载构建好的 ZIP 文件

### 创建发布版本

要创建正式的发布版本：

1. 创建并推送一个新标签：

```bash
git tag v1.0.0
git push origin v1.0.0
```

2. 这将自动触发构建，并创建一个 GitHub Release，用户可以从 "Releases" 页面下载可执行文件。

## 排查问题

如果构建失败：

1. 在 Actions 工作流程中查看失败的步骤日志
2. 常见问题包括依赖项安装失败或构建过程中的错误
3. 修复问题后，重新提交代码或手动触发工作流

## 注意事项

1. 构建的可执行文件不会自动签名，用户首次运行可能会收到 Windows 安全警告
2. 如果您的应用程序需要特殊权限，可能需要修改 workflow 文件
3. 大型依赖项可能会导致构建时间较长 